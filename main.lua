--[[ Full script: Multi-select + improved reliability + Tabbed Interface (Shop + Miscellaneous + Event)
   + English loading UI (no demo text)
   + Fake loading 2s BEFORE allowing main GUI to show
   + Do NOT show main GUI until both fake loading and initial populate finished
   + Added Pet Egg functionality with toggle buttons
   + Added Event tab with cooking automation
   + FIXED Event dropdown selection display issue
   + ADDED: Backpack search for food items matching menu list and hold before SubmitFood
   + CHANGED: Event dropdown supports MULTI-SELECT; automation uses any selected seeds
   + CHANGED: Event dropdown limited to max 5 selections and distribution/fallback logic for planting
]]
--// Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MarketplaceService = game:GetService("MarketplaceService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")
local player = LocalPlayer

-- === Loading GUI (show immediately) ===
local LoadingGUI = Instance.new("ScreenGui")
LoadingGUI.Name = "DepsoAutoBuyLoading"
LoadingGUI.ResetOnSpawn = false
LoadingGUI.Parent = PlayerGui
LoadingGUI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

local loadingOverlay = Instance.new("Frame")
loadingOverlay.Size = UDim2.new(1, 0, 1, 0)
loadingOverlay.Position = UDim2.new(0, 0, 0, 0)
loadingOverlay.BackgroundColor3 = Color3.fromRGB(0,0,0)
loadingOverlay.BackgroundTransparency = 0.55
loadingOverlay.BorderSizePixel = 0
loadingOverlay.Parent = LoadingGUI

local loadingBox = Instance.new("Frame")
loadingBox.Size = UDim2.new(0, 520, 0, 160) -- bigger box
loadingBox.Position = UDim2.new(0.5, -260, 0.5, -80)
loadingBox.BackgroundColor3 = Color3.fromRGB(28,28,28)
loadingBox.BorderSizePixel = 0
loadingBox.Parent = LoadingGUI
local UICorner = Instance.new("UICorner", loadingBox)
UICorner.CornerRadius = UDim.new(0, 12)

local loadingTitle = Instance.new("TextLabel")
loadingTitle.Size = UDim2.new(1, -24, 0, 48)
loadingTitle.Position = UDim2.new(0, 12, 0, 8)
loadingTitle.BackgroundTransparency = 1
loadingTitle.Text = "Loading..."
loadingTitle.TextColor3 = Color3.fromRGB(250,250,250)
loadingTitle.Font = Enum.Font.GothamBold
loadingTitle.TextSize = 28
loadingTitle.TextXAlignment = Enum.TextXAlignment.Center
loadingTitle.Parent = loadingBox

local loadingLabel = Instance.new("TextLabel")
loadingLabel.Size = UDim2.new(1, -24, 0, 28)
loadingLabel.Position = UDim2.new(0, 12, 0, 62)
loadingLabel.BackgroundTransparency = 1
loadingLabel.Text = "Preparing UI — please wait"
loadingLabel.TextColor3 = Color3.fromRGB(200,200,200)
loadingLabel.Font = Enum.Font.Gotham
loadingLabel.TextSize = 16
loadingLabel.TextXAlignment = Enum.TextXAlignment.Center
loadingLabel.Parent = loadingBox

-- animated dots
local loadingRunning = true
task.spawn(function()
    local dots = 0
    while loadingRunning do
        dots = dots + 1
        if dots > 3 then dots = 0 end
        local s = string.rep(".", dots)
        pcall(function() loadingTitle.Text = "Loading" .. s end)
        task.wait(0.45)
    end
end)

-- fake loading duration (seconds)
local FAKE_LOADING_SECONDS = 2
local fakeDone = false
task.spawn(function()
    task.wait(FAKE_LOADING_SECONDS)
    fakeDone = true
end)

-- continue with main script while LoadingGUI still visible
local GameInfo = (pcall(function() return MarketplaceService:GetProductInfo(game.PlaceId) end) and MarketplaceService:GetProductInfo(game.PlaceId)) or { Name = "Grow a Garden" }

--// Data
local SeedStock = {}
local GearStock = {}
local PetEggStock = {}
local SelectedSeeds = {} -- SelectedSeeds[name] = true/false
local SelectedGear = {} -- SelectedGear[name] = true/false
local SelectedPetEggs = {} -- SelectedPetEggs[name] = true/false

-- Event tab data
local SelectedEventSeeds = {} -- now a table for multi-select
local PlantQueue = {}
local MAX_PLANTS = 10

-- Auto Farm tab data
local SelectedAutoFarmSeeds = {} -- Multi-select seeds for auto farming
local AutoCollectEnabled = false

-- Improved Harvest Queue System
local HarvestQueue = {} -- คิวเก็บผล
local HarvestQueueIndex = 1 -- ตำแหน่งปัจจุบันในคิว
local LastQueueUpdate = 0 -- เวลาที่อัปเดตคิวล่าสุด
local QueueUpdateInterval = 5 -- อัปเดตคิวทุก 5 วินาที
local HarvestStats = {
    totalFound = 0,
    totalReady = 0,
    totalHarvested = 0,
    queueSize = 0,
    lastScanTime = 0
}

-- Global automation control to prevent multiple instances
local AutomationThreads = {}
local function StopAllAutomation()
    for threadName, threadData in pairs(AutomationThreads) do
        if threadData.running then
            threadData.running = false
            print("Stopped automation thread:", threadName)
        end
    end
    AutomationThreads = {}
end

local function StartAutomationThread(threadName, threadFunction)
    -- Stop existing thread if running
    if AutomationThreads[threadName] and AutomationThreads[threadName].running then
        AutomationThreads[threadName].running = false
    end

    -- Create new thread
    local threadData = { running = true }
    AutomationThreads[threadName] = threadData

    task.spawn(function()
        threadFunction(threadData)
    end)
end

-- Menu items to detect in Backpack
local FoodTargets = {
    "Soup","Sushi","Pizza","Burger","Sandwich","Hotdog",
    "Cake","Ice Cream","Donut","Waffle","Pie","Salad"
}

--// Remotes
local GameEvents = ReplicatedStorage:WaitForChild("GameEvents")

local function BuySeed(Seed: string)
    pcall(function()
        GameEvents.BuySeedStock:FireServer(Seed)
    end)
end

local function BuyGear(Gear: string)
    pcall(function()
        GameEvents.BuyGearStock:FireServer(Gear)
    end)
end

local function BuyPetEgg(EggName: string)
    pcall(function()
        GameEvents.BuyPetEgg:FireServer(EggName)
    end)
end

local function BuyAllSelectedSeeds()
    local selectedList = {}
    for name, sel in pairs(SelectedSeeds) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, seedName in ipairs(selectedList) do
        local stock = SeedStock[seedName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuySeed(seedName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedGear()
    local selectedList = {}
    for name, sel in pairs(SelectedGear) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, gearName in ipairs(selectedList) do
        local stock = GearStock[gearName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuyGear(gearName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedPetEggs()
    local selectedList = {}
    for name, sel in pairs(SelectedPetEggs) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, eggName in ipairs(selectedList) do
        BuyPetEgg(eggName)
        task.wait(0.15) -- Slightly longer delay for pet eggs
    end
end

-- Event functions
local PlantsInPot = 0

local function FindAndEquipSeed(seedName)
    local success = pcall(function()
        local backpack = Players.LocalPlayer.Backpack
        for _, item in pairs(backpack:GetChildren()) do
            if item.Name:lower():find(seedName:lower()) then
                item.Parent = Players.LocalPlayer.Character
                return true
            end
        end
        return false
    end)
    return success
end

-- NEW: Find and equip ANY selected seed from SelectedEventSeeds table.
-- Returns true if a seed was equipped.
local function FindAndEquipAnySelectedSeed(selectedSeedsTable)
    local ok, result = pcall(function()
        local backpack = Players.LocalPlayer and Players.LocalPlayer:FindFirstChild("Backpack")
        local character = Players.LocalPlayer and Players.LocalPlayer.Character
        local humanoid = character and character:FindFirstChildOfClass("Humanoid")
        if not backpack then return false end

        -- try each item in backpack; if it matches any selected seed, equip it and return true
        for _, item in ipairs(backpack:GetChildren()) do
            if not item or not item.Name then continue end
            for targetName, sel in pairs(selectedSeedsTable) do
                if sel and item.Name:lower():find(tostring(targetName):lower()) then
                    -- move to character
                    if character then
                        item.Parent = character
                    else
                        item.Parent = Players.LocalPlayer
                    end
                    -- try to equip if Tool
                    if humanoid and item:IsA("Tool") then
                        pcall(function() humanoid:EquipTool(item) end)
                    end
                    return true
                end
            end
        end
        return false
    end)
    if ok then return result end
    return false
end

-- NEW: Find and equip a specific seed name (exact matching via find)
local function FindAndEquipSpecificSeed(seedName)
    local ok, result = pcall(function()
        local backpack = Players.LocalPlayer and Players.LocalPlayer:FindFirstChild("Backpack")
        local character = Players.LocalPlayer and Players.LocalPlayer.Character
        local humanoid = character and character:FindFirstChildOfClass("Humanoid")
        if not backpack then return false end

        for _, item in ipairs(backpack:GetChildren()) do
            if not item or not item.Name then continue end
            if item.Name:lower():find(tostring(seedName):lower()) then
                if character then
                    item.Parent = character
                else
                    item.Parent = Players.LocalPlayer
                end
                if humanoid and item:IsA("Tool") then
                    pcall(function() humanoid:EquipTool(item) end)
                end
                return true
            end
        end
        return false
    end)
    if ok then return result end
    return false
end

local function SubmitPlant()
    pcall(function()
        local args = {"SubmitHeldPlant"}
        ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(args)
    end)
end

local function CookBest()
    pcall(function()
        local args = {"CookBest"}
        ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(args)
    end)
end

local function GetFoodFromPot()
    pcall(function()
        local args = {"GetFoodFromPot"}
        ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(args)
    end)
end

local function SubmitFood()
    pcall(function()
        local args = {"SubmitHeldFood"}
        ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("SubmitFoodService_RE"):FireServer(args)
    end)
end

-- Check if plant is ready to harvest and still exists
local function IsPlantReady(Plant)
    local ready = false
    pcall(function()
        -- First check if plant still exists in workspace
        if not Plant or not Plant.Parent then
            return false
        end

        -- Check for ProximityPrompt (most common)
        local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
        if Prompt and Prompt.Enabled and Prompt.Parent then
            ready = true
            return
        end

        -- Check for ClickDetector
        local ClickDetector = Plant:FindFirstChild("ClickDetector", true)
        if ClickDetector and ClickDetector.Parent then
            ready = true
            return
        end

        -- Check for visual indicators (like "Ready" text or specific colors)
        for _, desc in pairs(Plant:GetDescendants()) do
            if desc:IsA("TextLabel") and desc.Text and desc.Text:lower():find("ready") then
                ready = true
                return
            end
            if desc:IsA("BillboardGui") then
                local textLabel = desc:FindFirstChildOfClass("TextLabel")
                if textLabel and textLabel.Text and textLabel.Text:lower():find("ready") then
                    ready = true
                    return
                end
            end
        end
    end)
    return ready
end

-- Auto Farm harvest function with better validation
local function HarvestPlant(Plant)
    local success = false
    pcall(function()
        -- Check if plant still exists
        if not Plant or not Plant.Parent then
            print("Plant no longer exists")
            return false
        end

        -- First check if plant is ready
        if not IsPlantReady(Plant) then
            print("Plant not ready for harvest")
            return false
        end

        local beforeBackpackCount = 0
        local afterBackpackCount = 0

        -- Count items in backpack before harvest
        if Players.LocalPlayer.Backpack then
            beforeBackpackCount = #Players.LocalPlayer.Backpack:GetChildren()
        end

        -- Try multiple methods to find and trigger the harvest prompt
        local Prompt = Plant:FindFirstChild("ProximityPrompt", true)

        if Prompt and Prompt.Enabled and Prompt.Parent then
            -- Method 1: Direct fireproximityprompt
            fireproximityprompt(Prompt)
            task.wait(0.3) -- Wait for harvest to complete
            success = true
        else
            -- Method 2: Look for ClickDetector as backup
            local ClickDetector = Plant:FindFirstChild("ClickDetector", true)
            if ClickDetector and ClickDetector.Parent then
                fireclickdetector(ClickDetector)
                task.wait(0.3)
                success = true
            end
        end

        -- Method 3: Try to find harvest-related remotes
        if not success then
            local harvestRemote = ReplicatedStorage:FindFirstChild("GameEvents")
            if harvestRemote then
                local harvestEvent = harvestRemote:FindFirstChild("HarvestPlant") or
                                   harvestRemote:FindFirstChild("Harvest") or
                                   harvestRemote:FindFirstChild("CollectPlant")
                if harvestEvent then
                    harvestEvent:FireServer(Plant)
                    task.wait(0.3)
                    success = true
                end
            end
        end

        -- Verify harvest by checking backpack count
        if success and Players.LocalPlayer.Backpack then
            afterBackpackCount = #Players.LocalPlayer.Backpack:GetChildren()
            if afterBackpackCount > beforeBackpackCount then
                print("✓ Harvest confirmed - backpack items increased from", beforeBackpackCount, "to", afterBackpackCount)
            else
                print("⚠️ Harvest may have failed - no new items in backpack")
                success = false
            end
        end

        -- Check if plant was removed from workspace (another sign of successful harvest)
        task.wait(0.2)
        if not Plant.Parent then
            print("✓ Plant removed from workspace - harvest successful")
            success = true
        end
    end)
    return success
end

-- NEW: Build harvest queue by scanning all plants once
local function BuildHarvestQueue()
    HarvestQueue = {}
    HarvestQueueIndex = 1
    HarvestStats.totalFound = 0
    HarvestStats.totalReady = 0
    HarvestStats.lastScanTime = tick()

    if not AutoCollectEnabled then return end

    pcall(function()
        local Farm = workspace:FindFirstChild("Farm")
        if not Farm then
            print("🚫 Auto Farm: Farm not found in workspace")
            return
        end

        print("🔍 Building harvest queue - scanning all plants...")

        -- Recursive function to find all plants
        local function scanPlantsRecursive(container, depth)
            depth = depth or 0

            for _, child in pairs(container:GetChildren()) do
                if child:IsA("Model") and child.Name ~= "Important" then
                    local plantName = child.Name
                    HarvestStats.totalFound = HarvestStats.totalFound + 1

                    -- Check if plant matches selected seeds
                    for seedName, isSelected in pairs(SelectedAutoFarmSeeds) do
                        if isSelected and plantName:lower():find(seedName:lower()) then
                            -- Check if plant is ready
                            if IsPlantReady(child) then
                                HarvestStats.totalReady = HarvestStats.totalReady + 1
                                table.insert(HarvestQueue, {
                                    plant = child,
                                    name = plantName,
                                    seedType = seedName,
                                    position = child.PrimaryPart and child.PrimaryPart.Position or Vector3.new(0,0,0),
                                    addedTime = tick()
                                })
                                print("📋 Added to queue:", plantName, "(Ready)")
                            end
                            break
                        end
                    end
                elseif child:IsA("Folder") or (child:IsA("Model") and child.Name == "Important") then
                    scanPlantsRecursive(child, depth + 1)
                end
            end
        end

        -- Start scanning from Farm
        scanPlantsRecursive(Farm, 0)

        -- Also check Plants_Physical folders
        for _, subFolder in pairs(Farm:GetChildren()) do
            if subFolder:IsA("Folder") or subFolder:IsA("Model") then
                local Important = subFolder:FindFirstChild("Important")
                if Important then
                    local PlantsPhysical = Important:FindFirstChild("Plants_Physical")
                    if PlantsPhysical then
                        scanPlantsRecursive(PlantsPhysical, 1)
                    end
                end
            end
        end
    end)

    HarvestStats.queueSize = #HarvestQueue
    LastQueueUpdate = tick()

    print("📊 Queue built - Found:", HarvestStats.totalFound, "| Ready:", HarvestStats.totalReady, "| Queued:", HarvestStats.queueSize)
end

-- NEW: Process harvest queue one by one
local function ProcessHarvestQueue()
    if not AutoCollectEnabled or #HarvestQueue == 0 then return 0 end

    local harvestedCount = 0
    local maxHarvestPerCycle = 3 -- Limit harvests per cycle to prevent lag
    local harvested = 0

    while HarvestQueueIndex <= #HarvestQueue and harvested < maxHarvestPerCycle do
        local queueItem = HarvestQueue[HarvestQueueIndex]

        if queueItem and queueItem.plant and queueItem.plant.Parent then
            -- Double-check if plant is still ready
            if IsPlantReady(queueItem.plant) then
                print("🌾 Harvesting from queue:", queueItem.name, "(", HarvestQueueIndex, "/", #HarvestQueue, ")")

                local beforeCount = 0
                if Players.LocalPlayer.Backpack then
                    beforeCount = #Players.LocalPlayer.Backpack:GetChildren()
                end

                if HarvestPlant(queueItem.plant) then
                    task.wait(0.3) -- Wait for harvest to complete

                    local afterCount = 0
                    if Players.LocalPlayer.Backpack then
                        afterCount = #Players.LocalPlayer.Backpack:GetChildren()
                    end

                    if afterCount > beforeCount then
                        harvestedCount = harvestedCount + 1
                        harvested = harvested + 1
                        HarvestStats.totalHarvested = HarvestStats.totalHarvested + 1
                        print("✅ Successfully harvested:", queueItem.name, "(+" .. (afterCount - beforeCount) .. " items)")
                    else
                        print("⚠️ Harvest triggered but no items received:", queueItem.name)
                    end
                else
                    print("❌ Failed to harvest:", queueItem.name)
                end

                task.wait(0.2) -- Small delay between harvests
            else
                print("⏳ Plant no longer ready:", queueItem.name)
            end
        else
            print("🗑️ Plant no longer exists:", queueItem and queueItem.name or "unknown")
        end

        HarvestQueueIndex = HarvestQueueIndex + 1
    end

    -- If we've processed all items in queue, reset for next scan
    if HarvestQueueIndex > #HarvestQueue then
        print("✨ Queue completed! Harvested", harvestedCount, "plants this cycle")
        HarvestQueue = {}
        HarvestQueueIndex = 1
        LastQueueUpdate = 0 -- Force rebuild on next cycle
    end

    return harvestedCount
end

-- IMPROVED: Auto collect function using queue system
local function AutoCollectPlants()
    if not AutoCollectEnabled then return 0 end

    local currentTime = tick()

    -- Rebuild queue if needed (empty queue or interval passed)
    if #HarvestQueue == 0 or (currentTime - LastQueueUpdate) >= QueueUpdateInterval then
        BuildHarvestQueue()
    end

    -- Process items from queue
    return ProcessHarvestQueue()
end

-- Special status list for review food functionality
local SpecialStatusList = {
    "Gold", "Rainbow", "Wet", "Shocked", "Chilled", "Frozen", "Moonlit", "Bloodlit",
    "Celestial", "Zombified", "Disco", "Pollinated", "HoneyGlazed", "Voidtouched",
    "Twisted", "Plasma", "Heavenly", "Choc", "Meteoric", "Burnt", "Cooked", "Molten",
    "Dawnbound", "Alienlike", "Galactic", "Verdant", "Paradisal", "Sundried", "Windstruck",
    "Drenched", "Wilt", "Wiltproof", "Aurora", "Fried", "Cloudtouched", "Sandy", "Clay",
    "Ceramic", "Amber", "OldAmber", "AncientAmber", "Tempestuous", "Friendbound", "Infected",
    "Tranquil", "Chakra", "Foxfire Chakra", "Radioactive", "Corrupt", "Corrupt Chakra",
    "Corrupt Foxfire Chakra", "Harmonised Chakra", "Subzero", "Jackpot", "Touchdown",
    "Blitzshock", "Sliced", "Acidic", "Pasta", "Sauce", "Meatball", "Static", "Aromatic",
    "Junkshock", "Boil", "Oil"
}

-- Available rewards list for reward selection system
local AvailableRewardsList = {
    "Culinarian Chest", "Spring Onion", "Bitter Melon", "Pricklefruit", "Butternut Squash",
    "Cooking Cauldron", "Gourmet Seed Pack", "Gourmet Egg", "Kitchen Cart", "Kitchen Crate",
    "Kitchen Flooring", "Pet Shard Aromatic", "Gorilla Chef", "Sunny-Side Chicken", "Smoothie Fountain"
}



-- Check if held food has special status (optimized)
local function HasSpecialStatus()
    local character = Players.LocalPlayer.Character
    if not character then return false end

    for _, item in pairs(character:GetChildren()) do
        if item:IsA("Tool") and item.Name then
            local itemName = item.Name
            for _, status in ipairs(SpecialStatusList) do
                if itemName:find(status, 1, true) then
                    return true
                end
            end
        end
    end
    return false
end

-- Process all food in backpack - review special status food, submit normal food
local function ProcessAllFood()
    local backpack = Players.LocalPlayer.Backpack
    local character = Players.LocalPlayer.Character
    if not backpack or not character then return end

    local humanoid = character:FindFirstChildOfClass("Humanoid")
    if not humanoid then return end

    -- Pre-compile special status patterns for faster lookup
    local statusPatterns = {}
    for i, status in ipairs(SpecialStatusList) do
        statusPatterns[i] = status
    end

    -- Direct iteration without similarity scoring for better performance
    for _, item in pairs(backpack:GetChildren()) do
        if not item:IsA("Tool") then continue end

        local itemName = item.Name
        if not itemName then continue end

        -- Fast food type check using direct string matching
        local isFood = false
        local lowerName = itemName:lower()

        -- Exclude anything that contains "seed" from being treated as food
        if lowerName:find("seed", 1, true) then
            continue
        end

        for _, target in ipairs(FoodTargets) do
            if lowerName:find(target:lower(), 1, true) then
                isFood = true
                break
            end
        end

        if not isFood then continue end

        -- Move and equip item
        item.Parent = character
        humanoid:EquipTool(item)

        -- Fast special status check
        local hasSpecialStatus = false
        for _, status in ipairs(statusPatterns) do
            if itemName:find(status, 1, true) then
                hasSpecialStatus = true
                break
            end
        end

        -- Submit based on status
        if hasSpecialStatus then
            -- Review food
            ReplicatedStorage.GameEvents.SubmitFoodService_RE:FireServer("SubmitHeldFood", true)
            -- Async reward selection
            task.spawn(SelectReward)
        else
            -- Submit normal food
            ReplicatedStorage.GameEvents.SubmitFoodService_RE:FireServer("SubmitHeldFood")
        end
    end
end

-- Reward selection system
local SelectedRewards = {} -- User-selected rewards from available list

local function SelectReward()
    local playerGui = Players.LocalPlayer.PlayerGui
    local rewardsUI = playerGui:FindFirstChild("ChooseRewards_UI")
    if not rewardsUI then return end

    local items = rewardsUI.Frame and rewardsUI.Frame.Main and rewardsUI.Frame.Main.Items
    if not items then return end

    local itemsChildren = items:GetChildren()
    local availableRewards = {}

    -- Fast reward matching
    for i = 2, 4 do
        local slot = itemsChildren[i]
        if slot and slot.Title then
            local rewardName = slot.Title.Text
            for selectedReward, isSelected in pairs(SelectedRewards) do
                if isSelected and rewardName:find(selectedReward, 1, true) then
                    availableRewards[#availableRewards + 1] = {
                        slot = slot,
                        userSelected = selectedReward
                    }
                    break
                end
            end
        end
    end

    if #availableRewards == 0 then return end

    local chosenReward = nil
    local lowerSelected

    -- Priority: egg > seed pack > random
    for _, reward in ipairs(availableRewards) do
        lowerSelected = reward.userSelected:lower()
        if lowerSelected:find("egg", 1, true) then
            chosenReward = reward
            break
        end
    end

    if not chosenReward then
        for _, reward in ipairs(availableRewards) do
            lowerSelected = reward.userSelected:lower()
            if lowerSelected:find("seed pack", 1, true) then
                chosenReward = reward
                break
            end
        end
    end

    if not chosenReward then
        chosenReward = availableRewards[math.random(#availableRewards)]
    end

    -- Click the reward
    if chosenReward then
        local slot = chosenReward.slot
        local clickDetector = slot:FindFirstChildOfClass("ClickDetector")
        if clickDetector then
            clickDetector:FireServer()
        else
            local button = slot:FindFirstChildOfClass("TextButton") or slot:FindFirstChildOfClass("ImageButton")
            if button and button.MouseButton1Click then
                button.MouseButton1Click:Fire()
            end
        end
    end
end



-- Check if cooking timer is finished
local function IsCookingFinished()
    local success, result = pcall(function()
        local timerText = Players.LocalPlayer.PlayerGui.CookingTimerBB.Timer.Text
        return timerText == "Ready!" or timerText == "Ready" or timerText == "00:00:00"
    end)
    return success and result
end

-- Count current plants in queue
local function CountPlantsInQueue()
    return PlantsInPot
end

-- Check if any selected seed exists in backpack
local function HasAnySelectedSeedInBackpack(selectedSeedsTable)
    local ok, res = pcall(function()
        local backpack = Players.LocalPlayer and Players.LocalPlayer:FindFirstChild("Backpack")
        if not backpack then return false end
        for _, item in ipairs(backpack:GetChildren()) do
            for name, sel in pairs(selectedSeedsTable) do
                if sel and item.Name and item.Name:lower():find(tostring(name):lower()) then
                    return true
                end
            end
        end
        return false
    end)
    return ok and res
end

-- Seed reader
local function GetSeedStock()
    local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
    if not SeedShop then return {} end
    local sample = SeedShop:FindFirstChild("Blueberry", true)
    if not sample then return {} end
    local Items = sample.Parent

    local NewList = {}
    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        SeedStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end
    return NewList
end

-- Gear reader
local function GetGearStock()
    local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
    if not GearShop then return {} end

    -- Try to find any gear item to locate the parent container
    local sample = nil
    for _, child in pairs(GearShop:GetDescendants()) do
        if child:FindFirstChild("Main_Frame") and child:FindFirstChild("Main_Frame"):FindFirstChild("Stock_Text") then
            sample = child
            break
        end
    end

    if not sample then return {} end
    local Items = sample.Parent

    local NewList = {}
    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        GearStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end
    return NewList
end

-- Pet Egg reader
local function GetPetEggStock()
    local PetShopUI = PlayerGui:FindFirstChild("PetShop_UI")
    if not PetShopUI then return {} end

    local NewList = {}
    -- Look for actual egg items in PetShop_UI with proper filtering
    for _, child in pairs(PetShopUI:GetDescendants()) do
        if child:IsA("Frame") or child:IsA("TextButton") then
            local eggName = nil

            -- Check if it's a proper egg item (must contain "Egg" and not be UI elements)
            if child.Name:match(".*Egg$") and not child.Name:match("Shop") and not child.Name:match("Button") and not child.Name:match("Frame") then
                eggName = child.Name
            elseif child:FindFirstChild("EggName") and child.EggName:IsA("TextLabel") then
                local eggText = child.EggName.Text
                if eggText:match(".*Egg$") and not eggText:match("Shop") then
                    eggName = eggText
                end
            elseif child:FindFirstChildOfClass("TextLabel") then
                local textLabel = child:FindFirstChildOfClass("TextLabel")
                local labelText = textLabel.Text
                -- Only accept if it ends with "Egg" and doesn't contain UI-related terms
                if labelText:match(".*Egg$") and not labelText:match("Shop") and not labelText:match("Button") and labelText ~= "Pet Eggs" then
                    eggName = labelText
                end
            end

            -- Additional filtering to exclude obvious UI elements
            if eggName and eggName ~= "" and eggName ~= "Pet Egg Shop" and not eggName:match("^Pet Egg") then
                PetEggStock[eggName] = 1
                NewList[eggName] = 1
            end
        end
    end

    -- Remove duplicates and filter out non-egg items
    local filteredList = {}
    for name, count in pairs(NewList) do
        -- Final check: must end with "Egg" and not be a UI element
        if name:match(".*Egg$") and not name:match("Shop") and name ~= "Pet Eggs" then
            filteredList[name] = count
            PetEggStock[name] = count
        end
    end

    return filteredList
end


-- Event-driven stock watchers (optimize: hook shop UIs instead of polling)
local seedDirty, gearDirty, petDirty = false, false, false
local seedWatchConns, gearWatchConns, petWatchConns = {}, {}, {}

local function _clearConns(list)
    for _, c in ipairs(list) do
        pcall(function() c:Disconnect() end)
    end
    for i = #list, 1, -1 do list[i] = nil end
end

local function findItemsContainerWithStock(root)
    local found = nil
    pcall(function()
        for _, desc in ipairs(root:GetDescendants()) do
            local mf = desc:FindFirstChild("Main_Frame")
            if mf and mf:FindFirstChild("Stock_Text") then
                found = desc.Parent
                break
            end
        end
    end)
    return found
end

local function AttachSeedWatchers()
    _clearConns(seedWatchConns)
    local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
    if not SeedShop then return end
    local Items = findItemsContainerWithStock(SeedShop)
    if not Items then return end

    table.insert(seedWatchConns, Items.ChildAdded:Connect(function()
        seedDirty = true
    end))
    table.insert(seedWatchConns, Items.ChildRemoved:Connect(function()
        seedDirty = true
    end))

    -- Watch current stock labels
    for _, Item in ipairs(Items:GetChildren()) do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        local StockText = MainFrame and MainFrame:FindFirstChild("Stock_Text")
        if StockText and StockText:IsA("TextLabel") then
            table.insert(seedWatchConns, StockText:GetPropertyChangedSignal("Text"):Connect(function()
                seedDirty = true
            end))
        end
    end
end

local function AttachGearWatchers()
    _clearConns(gearWatchConns)
    local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
    if not GearShop then return end
    local Items = findItemsContainerWithStock(GearShop)
    if not Items then return end

    table.insert(gearWatchConns, Items.ChildAdded:Connect(function()
        gearDirty = true
    end))
    table.insert(gearWatchConns, Items.ChildRemoved:Connect(function()
        gearDirty = true
    end))

    for _, Item in ipairs(Items:GetChildren()) do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        local StockText = MainFrame and MainFrame:FindFirstChild("Stock_Text")
        if StockText and StockText:IsA("TextLabel") then
            table.insert(gearWatchConns, StockText:GetPropertyChangedSignal("Text"):Connect(function()
                gearDirty = true
            end))
        end
    end
end

local function AttachPetWatchers()
    _clearConns(petWatchConns)
    local PetShopUI = PlayerGui:FindFirstChild("PetShop_UI")
    if not PetShopUI then return end

    local function mark()
        petDirty = true
    end

    table.insert(petWatchConns, PetShopUI.DescendantAdded:Connect(mark))
    table.insert(petWatchConns, PetShopUI.DescendantRemoving:Connect(mark))
end

-- React to shop GUIs appearing/disappearing
PlayerGui.ChildAdded:Connect(function(child)
    if child.Name == "Seed_Shop" then
        AttachSeedWatchers()
    elseif child.Name == "Gear_Shop" then
        AttachGearWatchers()
    elseif child.Name == "PetShop_UI" then
        AttachPetWatchers()
    end
end)

PlayerGui.ChildRemoved:Connect(function(child)
    if child.Name == "Seed_Shop" then _clearConns(seedWatchConns) end
    if child.Name == "Gear_Shop" then _clearConns(gearWatchConns) end
    if child.Name == "PetShop_UI" then _clearConns(petWatchConns) end
end)

-- Try attaching immediately (if shops already open)
task.spawn(function()
    task.wait(0.5)
    pcall(AttachSeedWatchers)
    pcall(AttachGearWatchers)
    pcall(AttachPetWatchers)
end)

-- Helper: remove other custom GUIs (best-effort)
local function RemoveOtherCustomGUIs(keepThisName)
    for _, child in ipairs(PlayerGui:GetChildren()) do
        if child:IsA("ScreenGui") and child.Name ~= keepThisName then
            local foundWindow = child:FindFirstChild("Window", true)
            if foundWindow then
                for _, desc in ipairs(child:GetDescendants()) do
                    if (desc:IsA("LocalScript") or desc:IsA("Script")) and desc.Parent then
                        pcall(function()
                            if desc.Disabled ~= nil then desc.Disabled = true end
                        end)
                    end
                end
                pcall(function() child:Destroy() end)
            end
        end
    end
end

RemoveOtherCustomGUIs("DepsoAutoBuyUI_temp")

-- Stop any existing automation from previous script instances
StopAllAutomation()

-- === NEW: String distance & similarity helpers to match backpack items to menu ===
local function levenshtein(a, b)
    a = tostring(a)
    b = tostring(b)
    local la = #a
    local lb = #b
    if la == 0 then return lb end
    if lb == 0 then return la end

    local matrix = {}
    for i = 0, la do
        matrix[i] = {}
        matrix[i][0] = i
    end
    for j = 0, lb do
        matrix[0][j] = j
    end

    for i = 1, la do
        for j = 1, lb do
            local cost = (a:sub(i,i) == b:sub(j,j)) and 0 or 1
            local deletion = matrix[i-1][j] + 1
            local insertion = matrix[i][j-1] + 1
            local substitution = matrix[i-1][j-1] + cost
            matrix[i][j] = math.min(deletion, insertion, substitution)
        end
    end
    return matrix[la][lb]
end

local function normalizeString(s)
    return tostring(s):lower():gsub("%p", ""):gsub("%s+", " "):gsub("^%s*(.-)%s*$", "%1")
end

local function similarityScore(a, b)
    a = normalizeString(a)
    b = normalizeString(b)
    if a == "" or b == "" then return 0 end

    -- If one is substring of the other, treat as full match
    if a:find(b, 1, true) or b:find(a, 1, true) then
        return 1
    end

    local dist = levenshtein(a, b)
    local maxlen = math.max(#a, #b)
    if maxlen == 0 then return 0 end
    local score = 1 - (dist / maxlen)
    if score < 0 then score = 0 end
    return score
end

-- Find items in Backpack matching any name in targets and move/equip them.
-- targets: array of strings
-- threshold: 0..1
local function FindAndHoldFoodsFromList(targets, threshold)
    threshold = threshold or 0.65
    local matched = {}

    pcall(function()
        local backpack = Players.LocalPlayer and Players.LocalPlayer:FindFirstChild("Backpack")
        local character = Players.LocalPlayer and Players.LocalPlayer.Character
        local humanoid = character and character:FindFirstChildOfClass("Humanoid")

        if not backpack then return matched end

        for _, item in ipairs(backpack:GetChildren()) do
            if not item or not item.Name then continue end
            local itemName = item.Name
            for _, target in ipairs(targets) do
                local score = similarityScore(itemName, target)
                if score >= threshold then
                    local ok = pcall(function()
                        -- Move to character so it's considered held/equipped
                        if character then
                            item.Parent = character
                        else
                            item.Parent = Players.LocalPlayer -- fallback
                        end

                        -- If tool and humanoid present, try to equip
                        if humanoid and item:IsA("Tool") then
                            pcall(function() humanoid:EquipTool(item) end)
                        end
                    end)

                    if ok then
                        table.insert(matched, { Item = item, Target = target, Score = score })
                    end

                    break
                end
            end
        end
    end)

    return matched
end

--// UI setup (create UI and parent now, but hide main window until ready)
local UI = Instance.new("ScreenGui")
UI.Name = "DepsoAutoBuyUI"
UI.ResetOnSpawn = false
UI.Parent = PlayerGui
UI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

-- remove other GUIs (skip ours)
RemoveOtherCustomGUIs(UI.Name)

local function round(px)
    local cl = Instance.new("UICorner")
    cl.CornerRadius = UDim.new(0, px)
    return cl
end

local colors = {
    Window = Color3.fromRGB(30, 30, 30),
    Accent = Color3.fromRGB(70, 140, 40),
    AccentHover = Color3.fromRGB(90, 160, 60),
    Sub = Color3.fromRGB(40, 40, 40),
    Text = Color3.fromRGB(230, 230, 230),
    TabInactive = Color3.fromRGB(50, 50, 50),
}

-- Tab System Variables
local currentTab = "Shop"
local tabContents = {}

local function CreateWindow(title)
    local w = Instance.new("Frame")
    w.Name = "Window"
    w.Size = UDim2.new(0, 720, 0, 450) -- wider for 4 tabs
    w.Position = UDim2.new(0, 20, 0, 60)
    w.BackgroundColor3 = colors.Window
    w.BorderSizePixel = 0
    w.Parent = UI
    round(10):Clone().Parent = w

    local top = Instance.new("Frame")
    top.Name = "Top"
    top.Size = UDim2.new(1, 0, 0, 40)
    top.BackgroundColor3 = colors.Accent
    top.BorderSizePixel = 0
    top.Parent = w
    round(10):Clone().Parent = top

    local titleLbl = Instance.new("TextLabel")
    titleLbl.Size = UDim2.new(1, -84, 1, 0)
    titleLbl.Position = UDim2.new(0, 12, 0, 0)
    titleLbl.BackgroundTransparency = 1
    titleLbl.Text = title
    titleLbl.TextColor3 = colors.Text
    titleLbl.Font = Enum.Font.GothamBold
    titleLbl.TextSize = 16
    titleLbl.TextXAlignment = Enum.TextXAlignment.Left
    titleLbl.Parent = top

    local minimizeBtn = Instance.new("TextButton")
    minimizeBtn.Name = "Minimize"
    minimizeBtn.Size = UDim2.new(0, 40, 0, 26)
    minimizeBtn.Position = UDim2.new(1, -56, 0, 7)
    minimizeBtn.BackgroundColor3 = colors.Sub
    minimizeBtn.BorderSizePixel = 0
    minimizeBtn.Text = "-"
    minimizeBtn.TextColor3 = colors.Text
    minimizeBtn.Font = Enum.Font.GothamBold
    minimizeBtn.TextSize = 18
    minimizeBtn.Parent = top
    round(8):Clone().Parent = minimizeBtn

    -- Tab Container
    local tabContainer = Instance.new("Frame")
    tabContainer.Name = "TabContainer"
    tabContainer.Size = UDim2.new(1, -24, 0, 36)
    tabContainer.Position = UDim2.new(0, 12, 0, 48)
    tabContainer.BackgroundTransparency = 1
    tabContainer.Parent = w

    -- Content Container
    local content = Instance.new("Frame")
    content.Name = "Content"
    content.Size = UDim2.new(1, -24, 1, -120) -- adjusted for tabs
    content.Position = UDim2.new(0, 12, 0, 88)
    content.BackgroundColor3 = colors.Sub
    content.BorderSizePixel = 0
    content.Parent = w
    round(8):Clone().Parent = content

    return { Window = w, Top = top, TabContainer = tabContainer, Content = content, Title = titleLbl, Minimize = minimizeBtn }
end

local function CreateTab(parent, text, tabName, isActive, position)
    local tab = Instance.new("TextButton")
    tab.Name = tabName
    tab.Size = UDim2.new(0, 110, 1, 0)
    tab.Position = UDim2.new(0, position, 0, 0)
    tab.BackgroundColor3 = isActive and colors.Accent or colors.TabInactive
    tab.BorderSizePixel = 0
    tab.Text = text
    tab.TextColor3 = colors.Text
    tab.Font = Enum.Font.GothamBold
    tab.TextSize = 14
    tab.Parent = parent
    round(8):Clone().Parent = tab

    return tab
end

local function CreateLabel(parent, text, y)
    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -24, 0, 20)
    lbl.Position = UDim2.new(0, 12, 0, y or 6)
    lbl.BackgroundTransparency = 1
    lbl.Text = text
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = parent
    return lbl
end

local function CreateButton(parent, text, y, callback)
    local btn = Instance.new("TextButton")
    btn.Size = UDim2.new(1, -24, 0, 36)
    btn.Position = UDim2.new(0, 12, 0, y)
    btn.BackgroundColor3 = colors.Accent
    btn.Text = text
    btn.TextColor3 = Color3.new(1,1,1)
    btn.Font = Enum.Font.GothamBold
    btn.TextSize = 14
    btn.BorderSizePixel = 0
    btn.Parent = parent
    round(8):Clone().Parent = btn
    btn.MouseButton1Click:Connect(function() pcall(callback) end)
    return btn
end

local function CreateCheckbox(parent, label, initial, y)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 30)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(0, 24, 0, 24)
    box.Position = UDim2.new(0, 6, 0, 3)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Parent = container
    round(6):Clone().Parent = box

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -48, 1, 0)
    lbl.Position = UDim2.new(0, 40, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local checked = Instance.new("Frame")
    checked.Size = UDim2.new(1, -6, 1, -6)
    checked.Position = UDim2.new(0, 3, 0, 3)
    checked.BackgroundColor3 = colors.Accent
    checked.Visible = initial
    checked.Parent = box
    round(4):Clone().Parent = checked

    local state = { Value = initial }
    box.MouseButton1Click:Connect(function()
        state.Value = not state.Value
        checked.Visible = state.Value
    end)

    return state, container
end

-- FIXED CreateCombo with improved selection reliability for Event dropdown
-- Now supports a maxSelect parameter for multi-select limiting.
local function CreateCombo(parent, label, getItems, selectedTable, y, singleSelect, maxSelect)
    maxSelect = maxSelect or math.huge -- default no limit
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 80)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, 0, 0, 18)
    lbl.Position = UDim2.new(0, 0, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(1, 0, 0, 36)
    box.Position = UDim2.new(0, 0, 0, 22)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Active = true
    box.Parent = container
    round(8):Clone().Parent = box

    local selectedLabel = Instance.new("TextLabel")
    selectedLabel.Size = UDim2.new(1, -12, 1, 0)
    selectedLabel.Position = UDim2.new(0, 8, 0, 0)
    selectedLabel.BackgroundTransparency = 1
    selectedLabel.Text = singleSelect and "(select)" or "(select)"
    selectedLabel.TextColor3 = colors.Text
    selectedLabel.Font = Enum.Font.Gotham
    selectedLabel.TextSize = 13
    selectedLabel.TextXAlignment = Enum.TextXAlignment.Left
    selectedLabel.Parent = box

    local dropdown = Instance.new("Frame")
    dropdown.Name = "ComboDropdown"
    dropdown.Size = UDim2.fromOffset(260, 120)
    dropdown.Position = UDim2.fromOffset(0, 0)
    dropdown.BackgroundColor3 = colors.Sub
    dropdown.BorderSizePixel = 0
    dropdown.Visible = false
    dropdown.ZIndex = 50
    dropdown.Parent = UI
    round(8):Clone().Parent = dropdown

    local shadow = Instance.new("Frame")
    shadow.Size = UDim2.new(1, 6, 1, 6)
    shadow.Position = UDim2.new(0, -3, 0, -3)
    shadow.BackgroundTransparency = 0.88
    shadow.BackgroundColor3 = Color3.new(0,0,0)
    shadow.BorderSizePixel = 0
    shadow.Parent = dropdown
    round(10):Clone().Parent = shadow
    shadow.ZIndex = 49

    local scroll = Instance.new("ScrollingFrame")
    scroll.Size = UDim2.new(1, -8, 1, -8)
    scroll.Position = UDim2.new(0, 4, 0, 4)
    scroll.BackgroundTransparency = 1
    scroll.BorderSizePixel = 0
    scroll.Parent = dropdown
    scroll.ScrollBarThickness = 8
    scroll.AutomaticCanvasSize = Enum.AutomaticSize.Y
    scroll.ZIndex = 51

    local uiList = Instance.new("UIListLayout")
    uiList.Parent = scroll
    uiList.Padding = UDim.new(0, 6)
    uiList.SortOrder = Enum.SortOrder.LayoutOrder

    local open = false
    local isPopulating = false
    local lastPopulate = 0

    local function clampDropdownToScreen(px, py, w, h)
        local cam = workspace.CurrentCamera
        local screenW = cam and cam.ViewportSize.X or 800
        local screenH = cam and cam.ViewportSize.Y or 600
        if px + w > screenW then px = math.max(4, screenW - w - 4) end
        if py + h > screenH then py = math.max(4, screenH - h - 4) end
        return px, py
    end

    local function updateSelectedLabel()
        if singleSelect then
            if type(selectedTable) == "table" then
                for name, sel in pairs(selectedTable) do
                    if sel then
                        selectedLabel.Text = name
                        return
                    end
                end
                selectedLabel.Text = "(select)"
            else
                selectedLabel.Text = selectedTable ~= "" and selectedTable or "(select)"
            end
        else
            local names = {}
            for name, sel in pairs(selectedTable) do
                if sel then table.insert(names, name) end
            end
            if #names == 0 then
                selectedLabel.Text = "(select)"
            elseif #names <= 3 then
                selectedLabel.Text = table.concat(names, ", ")
            else
                selectedLabel.Text = names[1] .. ", " .. names[2] .. " +" .. tostring(#names - 2)
            end
        end
    end

    local function populate()
        if isPopulating then return end
        isPopulating = true
        lastPopulate = tick()

        for _, child in next, scroll:GetChildren() do
            if child:IsA("Frame") or child:IsA("TextButton") then child:Destroy() end
        end

        local items = {}
        pcall(function() items = getItems() end)

        local count = 0
        for name, _ in pairs(items) do
            count = count + 1
            local row = Instance.new("Frame")
            row.Size = UDim2.new(1, -8, 0, 30)
            row.LayoutOrder = count
            row.BackgroundTransparency = 1
            row.Parent = scroll

            local it = Instance.new("TextButton")
            it.Size = UDim2.new(1, 0, 1, 0)
            it.Position = UDim2.new(0, 0, 0, 0)
            it.BackgroundColor3 = colors.Window
            it.BorderSizePixel = 0
            it.AutoButtonColor = true
            it.Text = ""
            it.Active = true
            it.Selectable = true
            it.Parent = row
            round(6):Clone().Parent = it
            it.ZIndex = 52

            local nameLbl = Instance.new("TextLabel")
            nameLbl.Size = UDim2.new(1, -40, 1, 0)
            nameLbl.Position = UDim2.new(0, 8, 0, 0)
            nameLbl.BackgroundTransparency = 1
            nameLbl.Text = name
            nameLbl.TextColor3 = colors.Text
            nameLbl.Font = Enum.Font.Gotham
            nameLbl.TextSize = 13
            nameLbl.TextXAlignment = Enum.TextXAlignment.Left
            nameLbl.Parent = it
            nameLbl.ZIndex = 53

            local checkBox = Instance.new("Frame")
            checkBox.Size = UDim2.new(0, 24, 0, 24)
            checkBox.Position = UDim2.new(1, -34, 0, 3)
            checkBox.BackgroundColor3 = colors.Window
            checkBox.BorderSizePixel = 0
            checkBox.Parent = it
            round(6):Clone().Parent = checkBox
            checkBox.ZIndex = 53

            local tickFrame = Instance.new("Frame")
            tickFrame.Size = UDim2.new(1, -6, 1, -6)
            tickFrame.Position = UDim2.new(0, 3, 0, 3)
            tickFrame.BackgroundColor3 = colors.Accent

            if singleSelect then
                if type(selectedTable) == "table" then
                    tickFrame.Visible = selectedTable[name] == true
                else
                    tickFrame.Visible = (selectedTable == name)
                end
            else
                tickFrame.Visible = selectedTable[name] == true
            end

            tickFrame.Parent = checkBox
            round(4):Clone().Parent = tickFrame
            tickFrame.ZIndex = 54

            it.MouseEnter:Connect(function() it.BackgroundColor3 = Color3.fromRGB(60,60,60) end)
            it.MouseLeave:Connect(function() it.BackgroundColor3 = colors.Window end)

            local lastToggle = 0
            it.MouseButton1Click:Connect(function()
                local now = tick()
                if now - lastToggle < 0.1 then return end
                lastToggle = now

                if singleSelect then
                    if type(selectedTable) == "table" then
                        for k, _ in pairs(selectedTable) do selectedTable[k] = false end
                        selectedTable[name] = true
                        updateSelectedLabel()
                        for _, otherChild in pairs(scroll:GetChildren()) do
                            if otherChild:IsA("Frame") then
                                local otherButton = otherChild:FindFirstChild("TextButton")
                                if otherButton then
                                    local otherTick = otherButton:FindFirstChild("Frame")
                                    if otherTick and otherTick:FindFirstChild("Frame") then
                                        local otherTickFrame = otherTick:FindFirstChild("Frame")
                                        local otherLabel = otherButton:FindFirstChild("TextLabel")
                                        if otherLabel then
                                            otherTickFrame.Visible = selectedTable[otherLabel.Text] == true
                                        end
                                    end
                                end
                            end
                        end
                    else
                        selectedTable = name
                        updateSelectedLabel()
                    end
                else
                    -- Multi-select with limit
                    local currentlySelected = 0
                    for k,v in pairs(selectedTable) do if v then currentlySelected = currentlySelected + 1 end end

                    if not selectedTable[name] and currentlySelected >= maxSelect then
                        -- brief feedback
                        local old = selectedLabel.Text
                        selectedLabel.Text = "(max " .. tostring(maxSelect) .. " selected)"
                        task.spawn(function() task.wait(0.9); pcall(updateSelectedLabel) end)
                        return
                    end

                    selectedTable[name] = not selectedTable[name]
                    tickFrame.Visible = selectedTable[name] == true
                    updateSelectedLabel()
                end
            end)
        end

        task.spawn(function()
            task.wait()
            local contentHeight = uiList.AbsoluteContentSize.Y
            local desiredW = math.max(box.AbsoluteSize.X, 240)
            local desiredH = math.min(contentHeight + 12, 300)
            local boxAbsPos = box.AbsolutePosition
            local boxSize = box.AbsoluteSize
            local px, py = clampDropdownToScreen(boxAbsPos.X, boxAbsPos.Y + boxSize.Y + 6, desiredW, desiredH)
            dropdown.Position = UDim2.fromOffset(px, py)
            dropdown.Size = UDim2.fromOffset(desiredW, desiredH)
            isPopulating = false
        end)
    end

    box.MouseButton1Click:Connect(function()
        open = not open
        dropdown.Visible = open
        if open then
            populate()
        end
    end)

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if not open then return end
        if input.UserInputType ~= Enum.UserInputType.MouseButton1 and input.UserInputType ~= Enum.UserInputType.Touch then return end
        local pos = input.Position
        local mx, my = pos.X, pos.Y
        local absPos = dropdown.AbsolutePosition
        local absSize = dropdown.AbsoluteSize
        local bxPos = box.AbsolutePosition
        local bxSize = box.AbsoluteSize
        local inDropdown = (mx >= absPos.X and mx <= absPos.X + absSize.X and my >= absPos.Y and my <= absPos.Y + absSize.Y)
        local inBox = (mx >= bxPos.X and mx <= bxPos.X + bxSize.X and my >= bxPos.Y and my <= bxPos.Y + bxSize.Y)
        if not (inDropdown or inBox) then
            dropdown.Visible = false
            open = false
        end
    end)

    return { Container = container, Populate = populate, Dropdown = dropdown, Box = box, UpdateLabel = updateSelectedLabel, IsPopulating = function() return isPopulating end, LastPopulate = function() return lastPopulate end }
end

-- Build window (but keep hidden until ready)
local win = CreateWindow(GameInfo.Name .. " | Depso")
win.Window.Visible = false -- HIDE main GUI until ready

-- Create Tabs
local shopTab = CreateTab(win.TabContainer, "Shop", "Shop", true, 0)
local miscTab = CreateTab(win.TabContainer, "Miscellaneous", "Miscellaneous", false, 115)
local eventTab = CreateTab(win.TabContainer, "Event", "Event", false, 230)
local autoFarmTab = CreateTab(win.TabContainer, "Auto Farm", "AutoFarm", false, 345)

-- Create tab contents
local function createShopContent(parent)
    local content = Instance.new("Frame")
    content.Name = "ShopContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Parent = parent

    -- Seeds Section (Left side)
    local seedCombo = CreateCombo(content, "Seeds", function() return GetSeedStock() end, SelectedSeeds, 8)
    local seedAutoState, seedAutoFrame = CreateCheckbox(content, "Auto-Buy Seeds Enabled", false, 95)

    -- Pet Eggs Section (Left side, below seeds)
    local petEggCombo = CreateCombo(content, "Pet Eggs", function() return GetPetEggStock() end, SelectedPetEggs, 135)
    local petEggAutoState, petEggAutoFrame = CreateCheckbox(content, "Auto-Buy Pet Eggs Enabled", false, 222)

    -- Gear Section (Right side)
    -- Create gear content container on the right
    local gearContainer = Instance.new("Frame")
    gearContainer.Size = UDim2.new(0.5, -12, 1, 0)
    gearContainer.Position = UDim2.new(0.5, 12, 0, 0)
    gearContainer.BackgroundTransparency = 1
    gearContainer.Parent = content

    local gearCombo = CreateCombo(gearContainer, "Gear", function() return GetGearStock() end, SelectedGear, 8)
    local gearAutoState, gearAutoFrame = CreateCheckbox(gearContainer, "Auto-Buy Gear Enabled", false, 95)

    -- Adjust seeds section to left side only
    seedCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    seedAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)

    -- Adjust pet eggs section to left side only
    petEggCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    petEggAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)

    return {
        Content = content,
        SeedCombo = seedCombo,
        GearCombo = gearCombo,
        PetEggCombo = petEggCombo,
        SeedAutoState = seedAutoState,
        GearAutoState = gearAutoState,
        PetEggAutoState = petEggAutoState
    }
end

local function createMiscContent(parent)
    local content = Instance.new("Frame")
    content.Name = "MiscContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent

    -- Infinite Sprinkler Button
    local sprinklerBtn = CreateButton(content, "Infinite Sprinkler", 20, function()
        -- Simple infinite sprinkler - just delete sprinklers to reset effect
        local ObjectsFolder = workspace.Farm.Farm.Important.Objects_Physical
        local DeleteRemote = ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("DeleteObject")

        -- Find and delete all sprinklers
        for _, obj in ipairs(ObjectsFolder:GetChildren()) do
            if obj:IsA("Model") and obj.Name:lower():find("basic") then
                DeleteRemote:FireServer(obj)
            end
        end
    end)

    return { Content = content }
end

local function createEventContent(parent)
    local content = Instance.new("Frame")
    content.Name = "EventContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent

    -- Event Seed Selection (MULTI-SELECT) no limit
    local eventSeedCombo = CreateCombo(content, "Select Seeds for Event", function() return GetSeedStock() end, SelectedEventSeeds, 8, false)

    -- Auto Farm Toggle (5 steps: Submit Plants → Cook → Get Food → Process All Food)
    local autoFarmState, autoFarmFrame = CreateCheckbox(content, "Auto Farm (Submit Plants → Cook → Get Food → Process All Food)", false, 95)

    -- Reward Selection
    local rewardCombo = CreateCombo(content, "Select Desired Rewards", function()
        local rewardList = {}
        for _, reward in ipairs(AvailableRewardsList) do
            rewardList[reward] = 1
        end
        return rewardList
    end, SelectedRewards, 125, false)

    -- Status Label
    local statusLabel = CreateLabel(content, "Status: Ready", 215)
    statusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)

    -- Plants Count Label
    local plantsCountLabel = CreateLabel(content, "Plants in queue: 0/5", 240)
    plantsCountLabel.TextColor3 = Color3.fromRGB(200, 200, 200)

    return {
        Content = content,
        EventSeedCombo = eventSeedCombo,
        AutoFarmState = autoFarmState,
        RewardCombo = rewardCombo,
        StatusLabel = statusLabel,
        PlantsCountLabel = plantsCountLabel
    }
end

local function createAutoFarmContent(parent)
    local content = Instance.new("Frame")
    content.Name = "AutoFarmContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent

    -- Multi-select seed names for auto farming
    local autoFarmSeedCombo = CreateCombo(content, "Select Seeds to Auto Farm", function() return GetSeedStock() end, SelectedAutoFarmSeeds, 8, false)

    -- Auto Collect Toggle
    local autoCollectState, autoCollectFrame = CreateCheckbox(content, "Auto Collect (Harvest selected plants from Farm)", false, 95)

    -- Debug Farm Structure Button
    local debugButton = CreateButton(content, "Debug Farm Structure", 135, function()
        print("=== FARM DEBUG INFO ===")
        local Farm = workspace:FindFirstChild("Farm")
        if not Farm then
            print("❌ Farm not found in workspace!")
            return
        end

        print("✓ Farm found:", Farm.Name)
        print("Farm children count:", #Farm:GetChildren())

        for i, subFolder in pairs(Farm:GetChildren()) do
            print("  [" .. i .. "] " .. subFolder.Name .. " (" .. subFolder.ClassName .. ")")

            local Important = subFolder:FindFirstChild("Important")
            if Important then
                print("    ✓ Important found")
                local PlantsPhysical = Important:FindFirstChild("Plants_Physical")
                if PlantsPhysical then
                    print("    ✓ Plants_Physical found with", #PlantsPhysical:GetChildren(), "plants")
                    for j, plant in pairs(PlantsPhysical:GetChildren()) do
                        if j <= 5 then -- Show first 5 plants only
                            print("      Plant:", plant.Name, "(" .. plant.ClassName .. ")")
                            local prompt = plant:FindFirstChild("ProximityPrompt", true)
                            local click = plant:FindFirstChild("ClickDetector", true)
                            print("        ProximityPrompt:", prompt and "✓" or "❌")
                            print("        ClickDetector:", click and "✓" or "❌")
                        end
                    end
                    if #PlantsPhysical:GetChildren() > 5 then
                        print("      ... and", #PlantsPhysical:GetChildren() - 5, "more plants")
                    end
                else
                    print("    ❌ Plants_Physical not found")
                end
            else
                print("    ❌ Important not found")
            end
        end
        print("=== END DEBUG ===")
    end)

    -- Force Rebuild Queue Button
    local rebuildQueueButton = CreateButton(content, "🔄 Force Rebuild Queue", 180, function()
        print("🔄 Force rebuilding harvest queue...")
        HarvestQueue = {}
        HarvestQueueIndex = 1
        LastQueueUpdate = 0
        BuildHarvestQueue()
        print("✅ Queue rebuilt! Found " .. #HarvestQueue .. " plants ready to harvest")
    end)

    -- Check Backpack Button
    local checkBackpackButton = CreateButton(content, "Check Backpack Items", 220, function()
        print("=== BACKPACK CONTENTS ===")
        local backpack = Players.LocalPlayer.Backpack
        if not backpack then
            print("❌ Backpack not found!")
            return
        end

        local items = backpack:GetChildren()
        print("📦 Total items in backpack:", #items)

        local itemCounts = {}
        for _, item in pairs(items) do
            local itemName = item.Name
            itemCounts[itemName] = (itemCounts[itemName] or 0) + 1
        end

        for itemName, count in pairs(itemCounts) do
            print("  - " .. itemName .. " x" .. count)
        end

        if #items == 0 then
            print("📦 Backpack is empty!")
        end
        print("=== END BACKPACK ===")
    end)

    -- Status Label for Auto Farm
    local autoFarmStatusLabel = CreateLabel(content, "Status: Ready", 265)
    autoFarmStatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)

    -- Queue Status Label
    local queueStatusLabel = CreateLabel(content, "Queue: Empty", 290)
    queueStatusLabel.TextColor3 = Color3.fromRGB(150, 150, 150)

    -- Harvest Stats Label
    local harvestStatsLabel = CreateLabel(content, "Stats: 0 found | 0 ready | 0 harvested", 315)
    harvestStatsLabel.TextColor3 = Color3.fromRGB(150, 150, 150)

    -- Update AutoCollectEnabled when toggle changes
    autoCollectState.Value = AutoCollectEnabled

    return {
        Content = content,
        AutoFarmSeedCombo = autoFarmSeedCombo,
        AutoCollectState = autoCollectState,
        AutoFarmStatusLabel = autoFarmStatusLabel,
        QueueStatusLabel = queueStatusLabel,
        HarvestStatsLabel = harvestStatsLabel,
        DebugButton = debugButton,
        RebuildQueueButton = rebuildQueueButton,
        CheckBackpackButton = checkBackpackButton
    }
end

-- Create tab contents
tabContents.Shop = createShopContent(win.Content)
tabContents.Miscellaneous = createMiscContent(win.Content)
tabContents.Event = createEventContent(win.Content)
tabContents.AutoFarm = createAutoFarmContent(win.Content)

-- Tab switching logic
local function switchTab(tabName)
    if currentTab == tabName then return end

    -- Update tab buttons
    for _, tab in pairs({shopTab, miscTab, eventTab, autoFarmTab}) do
        if tab.Name == tabName then
            tab.BackgroundColor3 = colors.Accent
        else
            tab.BackgroundColor3 = colors.TabInactive
        end
    end

    -- Update content visibility
    for name, tabContent in pairs(tabContents) do
        tabContent.Content.Visible = (name == tabName)
    end

    currentTab = tabName
end

-- Connect tab buttons
shopTab.MouseButton1Click:Connect(function() switchTab("Shop") end)
miscTab.MouseButton1Click:Connect(function() switchTab("Miscellaneous") end)
eventTab.MouseButton1Click:Connect(function() switchTab("Event") end)
autoFarmTab.MouseButton1Click:Connect(function() switchTab("AutoFarm") end)

-- drag & minimize
local dragging = false
local dragStart, startPos, dragConn
local isMinimized = false
local prevSize = win.Window.Size
local prevContentVisible = true

local function updateDrag(input)
    if not dragging or not dragStart or not startPos then return end
    local delta = input.Position - dragStart
    local newX = startPos.X.Offset + delta.X
    local newY = startPos.Y.Offset + delta.Y
    win.Window.Position = UDim2.new(0, newX, 0, newY)
end

win.Top.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
        dragging = true
        dragStart = input.Position
        startPos = win.Window.Position
        dragConn = input.Changed:Connect(function() if input.UserInputState == Enum.UserInputState.End then dragging = false end end)
    end
end)

UserInputService.InputChanged:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
        if dragging then pcall(updateDrag, input) end
    end
end)

UserInputService.InputEnded:Connect(function(input)
    if dragConn and input.UserInputType == Enum.UserInputType.MouseButton1 then dragConn:Disconnect(); dragConn = nil end
end)

win.Minimize.MouseButton1Click:Connect(function()
    isMinimized = not isMinimized
    if isMinimized then
        prevSize = win.Window.Size
        prevContentVisible = win.Content.Visible
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = UDim2.new(win.Window.Size.X.Scale, win.Window.Size.X.Offset, 0, win.Top.Size.Y.Offset + 6) }):Play()
        win.Content.Visible = false
        win.TabContainer.Visible = false
        win.Minimize.Text = "+"
    else
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = prevSize }):Play()
        win.Content.Visible = prevContentVisible
        win.TabContainer.Visible = true
        win.Minimize.Text = "-"
    end
end)

-- hide combo dropdowns if window moves
win.Window:GetPropertyChangedSignal("Position"):Connect(function()
    local shopContent = tabContents.Shop
    local eventContent = tabContents.Event
    local autoFarmContent = tabContents.AutoFarm

    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Dropdown and shopContent.SeedCombo.Dropdown.Visible then
            shopContent.SeedCombo.Dropdown.Visible = false
        end
        if shopContent.GearCombo and shopContent.GearCombo.Dropdown and shopContent.GearCombo.Dropdown.Visible then
            shopContent.GearCombo.Dropdown.Visible = false
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Dropdown and shopContent.PetEggCombo.Dropdown.Visible then
            shopContent.PetEggCombo.Dropdown.Visible = false
        end
    end

    if eventContent then
        if eventContent.EventSeedCombo and eventContent.EventSeedCombo.Dropdown and eventContent.EventSeedCombo.Dropdown.Visible then
            eventContent.EventSeedCombo.Dropdown.Visible = false
        end
        if eventContent.RewardCombo and eventContent.RewardCombo.Dropdown and eventContent.RewardCombo.Dropdown.Visible then
            eventContent.RewardCombo.Dropdown.Visible = false
        end
    end

    if autoFarmContent then
        if autoFarmContent.AutoFarmSeedCombo and autoFarmContent.AutoFarmSeedCombo.Dropdown and autoFarmContent.AutoFarmSeedCombo.Dropdown.Visible then
            autoFarmContent.AutoFarmSeedCombo.Dropdown.Visible = false
        end
    end
end)

-- initial populate and wait for completion; only show main GUI after both fakeDone and populate finished
local function waitForInitialPopulateAndFake()
    -- Populate shop combos
    local shopContent = tabContents.Shop
    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Populate then
            shopContent.SeedCombo.Populate()
        end
        if shopContent.GearCombo and shopContent.GearCombo.Populate then
            shopContent.GearCombo.Populate()
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Populate then
            shopContent.PetEggCombo.Populate()
        end
    end

    -- Populate event combo
    local eventContent = tabContents.Event
    if eventContent and eventContent.EventSeedCombo and eventContent.EventSeedCombo.Populate then
        eventContent.EventSeedCombo.Populate()
    end

    -- Populate reward combo
    if eventContent and eventContent.RewardCombo and eventContent.RewardCombo.Populate then
        eventContent.RewardCombo.Populate()
    end

    -- Populate auto farm combo
    local autoFarmContent = tabContents.AutoFarm
    if autoFarmContent and autoFarmContent.AutoFarmSeedCombo and autoFarmContent.AutoFarmSeedCombo.Populate then
        autoFarmContent.AutoFarmSeedCombo.Populate()
    end

    -- Wait for populate finish or timeout
    local t0 = tick()
    local timeout = 8
    while tick() - t0 < timeout do
        local allDone = true
        if shopContent then
            if shopContent.SeedCombo and shopContent.SeedCombo.IsPopulating and shopContent.SeedCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.GearCombo and shopContent.GearCombo.IsPopulating and shopContent.GearCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.PetEggCombo and shopContent.PetEggCombo.IsPopulating and shopContent.PetEggCombo.IsPopulating() then
                allDone = false
            end
        end
        if eventContent and eventContent.EventSeedCombo and eventContent.EventSeedCombo.IsPopulating and eventContent.EventSeedCombo.IsPopulating() then
            allDone = false
        end
        if eventContent and eventContent.RewardCombo and eventContent.RewardCombo.IsPopulating and eventContent.RewardCombo.IsPopulating() then
            allDone = false
        end
        local autoFarmContent = tabContents.AutoFarm
        if autoFarmContent and autoFarmContent.AutoFarmSeedCombo and autoFarmContent.AutoFarmSeedCombo.IsPopulating and autoFarmContent.AutoFarmSeedCombo.IsPopulating() then
            allDone = false
        end
        if allDone then break end
        task.wait(0.05)
    end

    -- Also wait until fake timer done
    local tstart = tick()
    while not fakeDone and (tick() - tstart) < (FAKE_LOADING_SECONDS + 10) do
        task.wait(0.05)
    end
end

waitForInitialPopulateAndFake()

-- now it's safe to show main GUI
win.Window.Visible = true

-- stop loading animation and destroy loading GUI
loadingRunning = false
pcall(function() LoadingGUI:Destroy() end)

-- periodic refresh: update Stock always; only repopulate if dropdown closed or safe
StartAutomationThread("PeriodicRefresh", function(threadData)
    while threadData.running and task.wait(0.6) do
        pcall(function()
            -- Update stock only when watchers mark dirty
            local doSeed, doGear, doPet = seedDirty, gearDirty, petDirty

            if doSeed then
                GetSeedStock()
                seedDirty = false
            end
            if doGear then
                GetGearStock()
                gearDirty = false
            end
            if doPet then
                GetPetEggStock()
                petDirty = false
            end

            -- Refresh combos for shop tab
            local shopContent = tabContents.Shop
            if shopContent then
                -- Refresh seed combo
                if shopContent.SeedCombo and doSeed then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.SeedCombo.IsPopulating and shopContent.SeedCombo.IsPopulating() then canPopulate = false end
                        if shopContent.SeedCombo.LastPopulate and (tick() - shopContent.SeedCombo.LastPopulate()) < 0.8 then canPopulate = false end
                        if not shopContent.SeedCombo.Dropdown.Visible then
                            shopContent.SeedCombo.Populate()
                        elseif canPopulate then
                            shopContent.SeedCombo.Populate()
                        end
                        if shopContent.SeedCombo.UpdateLabel then shopContent.SeedCombo.UpdateLabel() end
                    end)
                end

                -- Refresh gear combo
                if shopContent.GearCombo and doGear then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.GearCombo.IsPopulating and shopContent.GearCombo.IsPopulating() then canPopulate = false end
                        if shopContent.GearCombo.LastPopulate and (tick() - shopContent.GearCombo.LastPopulate()) < 0.8 then canPopulate = false end
                        if not shopContent.GearCombo.Dropdown.Visible then
                            shopContent.GearCombo.Populate()
                        elseif canPopulate then
                            shopContent.GearCombo.Populate()
                        end
                        if shopContent.GearCombo.UpdateLabel then shopContent.GearCombo.UpdateLabel() end
                    end)
                end

                -- Refresh pet egg combo
                if shopContent.PetEggCombo and doPet then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.PetEggCombo.IsPopulating and shopContent.PetEggCombo.IsPopulating() then canPopulate = false end
                        if shopContent.PetEggCombo.LastPopulate and (tick() - shopContent.PetEggCombo.LastPopulate()) < 0.8 then canPopulate = false end
                        if not shopContent.PetEggCombo.Dropdown.Visible then
                            shopContent.PetEggCombo.Populate()
                        elseif canPopulate then
                            shopContent.PetEggCombo.Populate()
                        end
                        if shopContent.PetEggCombo.UpdateLabel then shopContent.PetEggCombo.UpdateLabel() end
                    end)
                end
            end

            -- Refresh event combo
            local eventContent = tabContents.Event
            if eventContent and eventContent.EventSeedCombo and doSeed then
                pcall(function()
                    local canPopulate = true
                    if eventContent.EventSeedCombo.IsPopulating and eventContent.EventSeedCombo.IsPopulating() then canPopulate = false end
                    if eventContent.EventSeedCombo.LastPopulate and (tick() - eventContent.EventSeedCombo.LastPopulate()) < 0.8 then canPopulate = false end
                    if not eventContent.EventSeedCombo.Dropdown.Visible then
                        eventContent.EventSeedCombo.Populate()
                    elseif canPopulate then
                        eventContent.EventSeedCombo.Populate()
                    end
                    if eventContent.EventSeedCombo.UpdateLabel then eventContent.EventSeedCombo.UpdateLabel() end
                end)
            end

            -- Refresh reward combo
            if eventContent and eventContent.RewardCombo then
                pcall(function()
                    local canPopulate = true
                    if eventContent.RewardCombo.IsPopulating and eventContent.RewardCombo.IsPopulating() then canPopulate = false end
                    if eventContent.RewardCombo.LastPopulate and (tick() - eventContent.RewardCombo.LastPopulate()) < 0.8 then canPopulate = false end
                    if not eventContent.RewardCombo.Dropdown.Visible then
                        eventContent.RewardCombo.Populate()
                    elseif canPopulate then
                        eventContent.RewardCombo.Populate()
                    end
                    if eventContent.RewardCombo.UpdateLabel then eventContent.RewardCombo.UpdateLabel() end
                end)
            end

            -- Refresh auto farm combo
            local autoFarmContent = tabContents.AutoFarm
            if autoFarmContent and autoFarmContent.AutoFarmSeedCombo and doSeed then
                pcall(function()
                    local canPopulate = true
                    if autoFarmContent.AutoFarmSeedCombo.IsPopulating and autoFarmContent.AutoFarmSeedCombo.IsPopulating() then canPopulate = false end
                    if autoFarmContent.AutoFarmSeedCombo.LastPopulate and (tick() - autoFarmContent.AutoFarmSeedCombo.LastPopulate()) < 0.8 then canPopulate = false end
                    if not autoFarmContent.AutoFarmSeedCombo.Dropdown.Visible then
                        autoFarmContent.AutoFarmSeedCombo.Populate()
                    elseif canPopulate then
                        autoFarmContent.AutoFarmSeedCombo.Populate()
                    end
                    if autoFarmContent.AutoFarmSeedCombo.UpdateLabel then autoFarmContent.AutoFarmSeedCombo.UpdateLabel() end
                end)
            end
        end)
    end
end)

-- auto-buy loops
StartAutomationThread("AutoBuy", function(threadData)
    while threadData.running and task.wait(1) do
        local shopContent = tabContents.Shop
        if shopContent then
            -- Auto-buy seeds if enabled
            if shopContent.SeedAutoState and shopContent.SeedAutoState.Value then
                pcall(BuyAllSelectedSeeds)
            end
            -- Auto-buy gear if enabled
            if shopContent.GearAutoState and shopContent.GearAutoState.Value then
                pcall(BuyAllSelectedGear)
            end
            -- Auto-buy pet eggs if enabled
            if shopContent.PetEggAutoState and shopContent.PetEggAutoState.Value then
                pcall(BuyAllSelectedPetEggs)
            end
        end
    end
end)

-- Event automation loop (5 steps: Submit Plants → Cook → Get Food → Process All Food)
StartAutomationThread("EventAutomation", function(threadData)
    local eventPhase = "submit_plants" -- "submit_plants", "cooking", "get_food", "process_all_food"
    local lastAction = 0

    while threadData.running and task.wait(0.3) do
        local eventContent = tabContents.Event
        if not eventContent or not eventContent.AutoFarmState or not eventContent.AutoFarmState.Value then
            continue
        end

        -- Check if any seed is selected
        local anySelected = false
        for k, v in pairs(SelectedEventSeeds) do
            if v then anySelected = true; break end
        end

        if not anySelected then
            if eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Please select at least one seed"
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            continue
        end

        local now = tick()
        local isCookingFinished = IsCookingFinished()

        -- Update plants count
        if eventContent.PlantsCountLabel then
            eventContent.PlantsCountLabel.Text = "Plants in queue: " .. PlantsInPot .. "/" .. MAX_PLANTS
        end

        -- Phase 1: Submit Plants (up to MAX_PLANTS) with distribution & fallback
        if eventPhase == "submit_plants" then
            if eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Adding plants to pot (" .. PlantsInPot .. "/" .. tostring(MAX_PLANTS) .. ")"
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
            end

            -- collect selected seeds in a deterministic order (convert to array)
            local selectedNames = {}
            for name, sel in pairs(SelectedEventSeeds) do
                if sel then table.insert(selectedNames, name) end
            end

            -- if none selected, prompt (shouldn't happen because checked earlier)
            if #selectedNames == 0 then
                if eventContent.StatusLabel then
                    eventContent.StatusLabel.Text = "Status: Please select at least one seed"
                    eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
                end
                continue
            end

            -- compute desired distribution across selected seeds
            local n = #selectedNames
            local base = math.floor(MAX_PLANTS / n)
            local rem = MAX_PLANTS % n
            local desiredCounts = {}
            for i, name in ipairs(selectedNames) do
                desiredCounts[name] = base + (i <= rem and 1 or 0)
            end

            -- Try to fill according to desiredCounts (bounded attempts to avoid long block)
            local anyPlanted = false
            local attempts = 0
            while PlantsInPot < MAX_PLANTS and attempts < 8 do
                attempts = attempts + 1
                local plantedThisPass = false

                -- try each selected seed according to its desired remaining
                for _, seedName in ipairs(selectedNames) do
                    if PlantsInPot >= MAX_PLANTS then break end
                    if desiredCounts[seedName] and desiredCounts[seedName] > 0 then
                        if FindAndEquipSpecificSeed(seedName) then
                            task.wait(0.1)
                            SubmitPlant()
                            desiredCounts[seedName] = desiredCounts[seedName] - 1
                            PlantsInPot = PlantsInPot + 1
                            plantedThisPass = true
                            anyPlanted = true
                            task.wait(0.1)
                        end
                    end
                end

                if PlantsInPot >= MAX_PLANTS then break end

                -- If nothing planted this pass, attempt to let any selected seed fill remaining
                if not plantedThisPass then
                    -- if none of selected seeds exist in backpack
                    if not HasAnySelectedSeedInBackpack(SelectedEventSeeds) then
                        if PlantsInPot == 0 then
                            if eventContent.StatusLabel then
                                eventContent.StatusLabel.Text = "Status: No selected seeds in backpack (waiting...)"
                                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200,100,100)
                            end
                            break -- stay in this phase and retry next loop
                        else
                            -- some planted but not full: proceed to cooking as requested
                            break
                        end
                    end

                    -- try to equip any available selected seed for remaining slots
                    local filledAny = false
                    for i = 1, (MAX_PLANTS - PlantsInPot) do
                        if FindAndEquipAnySelectedSeed(SelectedEventSeeds) then
                            task.wait(0.1)
                            SubmitPlant()
                            PlantsInPot = PlantsInPot + 1
                            filledAny = true
                            anyPlanted = true
                            task.wait(0.1)
                        else
                            break
                        end
                        if PlantsInPot >= MAX_PLANTS then break end
                    end

                    if not filledAny then
                        -- can't fill further right now
                        break
                    end
                end
            end

            -- if at least one plant was added OR full, move to cook; if none added and none exists, we already set status and will retry
            if PlantsInPot >= MAX_PLANTS or anyPlanted then
                CookBest()
                eventPhase = "cooking"
                lastAction = now
            end

        -- Phase 2: Wait for cooking to finish
        elseif eventPhase == "cooking" then
            if eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Cooking in progress..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 150, 100)
            end

            if isCookingFinished and (now - lastAction) > 0.5 then
                GetFoodFromPot()
                eventPhase = "get_food"
                lastAction = now
            end

        -- Phase 3: Get food from pot
        elseif eventPhase == "get_food" then
            if eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Getting food from pot..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
            end

            if (now - lastAction) > 1 then
                GetFoodFromPot()
                eventPhase = "process_all_food"
                lastAction = now
            end

        -- Phase 4: Process all food in backpack
        elseif eventPhase == "process_all_food" then
            if eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Processing all food in backpack..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 150)
            end

            if (now - lastAction) > 1 then
                -- Process all food items in backpack
                ProcessAllFood()

                -- Reset for next cycle
                PlantsInPot = 0
                eventPhase = "submit_plants"
                lastAction = now

                if eventContent.StatusLabel then
                    eventContent.StatusLabel.Text = "Status: Cycle complete - starting new cycle"
                    eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
                end
            end
        end
    end
end)

-- IMPROVED: Auto Farm automation loop with queue system
StartAutomationThread("AutoFarm", function(threadData)
    local totalHarvested = 0
    local lastHarvestTime = 0
    local lastQueueBuildTime = 0

    while threadData.running and task.wait(1.5) do -- Check every 1.5 seconds for better responsiveness
        local autoFarmContent = tabContents.AutoFarm
        if not autoFarmContent then continue end

        -- Update AutoCollectEnabled from the toggle state
        AutoCollectEnabled = autoFarmContent.AutoCollectState and autoFarmContent.AutoCollectState.Value or false

        if not AutoCollectEnabled then
            if autoFarmContent.AutoFarmStatusLabel then
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Auto Collect disabled"
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            if autoFarmContent.QueueStatusLabel then
                autoFarmContent.QueueStatusLabel.Text = "Queue: Disabled"
                autoFarmContent.QueueStatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            if autoFarmContent.HarvestStatsLabel then
                autoFarmContent.HarvestStatsLabel.Text = "Stats: Auto collect is disabled"
                autoFarmContent.HarvestStatsLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            continue
        end

        -- Check if any seeds are selected
        local anySelected = false
        local selectedSeeds = {}
        for k, v in pairs(SelectedAutoFarmSeeds) do
            if v then
                anySelected = true
                table.insert(selectedSeeds, k)
            end
        end

        if not anySelected then
            if autoFarmContent.AutoFarmStatusLabel then
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Please select at least one seed"
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            if autoFarmContent.QueueStatusLabel then
                autoFarmContent.QueueStatusLabel.Text = "Queue: No seeds selected"
                autoFarmContent.QueueStatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
            end
            continue
        end

        -- Update status based on queue state
        local currentTime = tick()
        local queueSize = #HarvestQueue
        local queueProgress = queueSize > 0 and HarvestQueueIndex or 0

        if autoFarmContent.AutoFarmStatusLabel then
            if queueSize > 0 then
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Processing queue (" .. table.concat(selectedSeeds, ", ") .. ")"
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(100, 200, 255)
            else
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Scanning for plants (" .. table.concat(selectedSeeds, ", ") .. ")"
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
            end
        end

        -- Update queue status
        if autoFarmContent.QueueStatusLabel then
            if queueSize > 0 then
                local remaining = math.max(0, queueSize - queueProgress + 1)
                autoFarmContent.QueueStatusLabel.Text = "Queue: " .. queueProgress .. "/" .. queueSize .. " (Remaining: " .. remaining .. ")"
                autoFarmContent.QueueStatusLabel.TextColor3 = Color3.fromRGB(100, 200, 255)
            else
                local timeSinceLastScan = currentTime - HarvestStats.lastScanTime
                if timeSinceLastScan < 10 then
                    autoFarmContent.QueueStatusLabel.Text = "Queue: Empty - waiting for plants to grow"
                    autoFarmContent.QueueStatusLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
                else
                    autoFarmContent.QueueStatusLabel.Text = "Queue: Building..."
                    autoFarmContent.QueueStatusLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
                end
            end
        end

        -- Perform auto collect using improved queue system
        local harvestedThisRound = AutoCollectPlants()
        if harvestedThisRound > 0 then
            totalHarvested = totalHarvested + harvestedThisRound
            lastHarvestTime = tick()
        end

        -- Update harvest stats
        if autoFarmContent.HarvestStatsLabel then
            local timeSinceLastHarvest = tick() - lastHarvestTime
            if harvestedThisRound > 0 then
                autoFarmContent.HarvestStatsLabel.Text = "Stats: " .. HarvestStats.totalFound .. " found | " .. HarvestStats.totalReady .. " ready | " .. HarvestStats.totalHarvested .. " harvested (+" .. harvestedThisRound .. " this cycle)"
                autoFarmContent.HarvestStatsLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
            else
                autoFarmContent.HarvestStatsLabel.Text = "Stats: " .. HarvestStats.totalFound .. " found | " .. HarvestStats.totalReady .. " ready | " .. HarvestStats.totalHarvested .. " harvested"
                if timeSinceLastHarvest < 30 then
                    autoFarmContent.HarvestStatsLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
                else
                    autoFarmContent.HarvestStatsLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
                end
            end
        end

        -- Update main status based on recent activity
        if autoFarmContent.AutoFarmStatusLabel and harvestedThisRound == 0 then
            local timeSinceLastHarvest = tick() - lastHarvestTime
            if timeSinceLastHarvest < 30 then
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Active - Total harvested: " .. HarvestStats.totalHarvested
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
            elseif queueSize == 0 then
                autoFarmContent.AutoFarmStatusLabel.Text = "Status: Waiting for plants to grow..."
                autoFarmContent.AutoFarmStatusLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
            end
        end
    end
end)